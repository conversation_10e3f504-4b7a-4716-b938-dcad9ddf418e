D:\vscode\E7\E7gearOCR\src-tauri\target\debug\deps\e7gearocr_lib-ad49c0d04d1e54b0.d: src\lib.rs src\config.rs src\adb.rs src\detection.rs src\image.rs src\ocr.rs src\equipment.rs src\evaluator.rs src\commands.rs D:\vscode\E7\E7gearOCR\src-tauri\target\debug\build\e7gearocr-747bff0008ec8532\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\vscode\E7\E7gearOCR\src-tauri\target\debug\deps\libe7gearocr_lib-ad49c0d04d1e54b0.rmeta: src\lib.rs src\config.rs src\adb.rs src\detection.rs src\image.rs src\ocr.rs src\equipment.rs src\evaluator.rs src\commands.rs D:\vscode\E7\E7gearOCR\src-tauri\target\debug\build\e7gearocr-747bff0008ec8532\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
src\config.rs:
src\adb.rs:
src\detection.rs:
src\image.rs:
src\ocr.rs:
src\equipment.rs:
src\evaluator.rs:
src\commands.rs:
D:\vscode\E7\E7gearOCR\src-tauri\target\debug\build\e7gearocr-747bff0008ec8532\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=eloy
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=e7gearocr
# env-dep:OUT_DIR=D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\e7gearocr-747bff0008ec8532\\out
