[package]
name = "e7gearocr"
version = "0.1.0"
description = "A Tauri App"
authors = ["eloy"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "e7gearocr_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
adb_client = "2.1.16"
anyhow = "1.0.98"
image = "0.25.6"
imageproc = "0.25.0"
once_cell = "1.21.3"
regex = "1.11.1"
tokio = { version = "1.47.1", features = ["full"] }

